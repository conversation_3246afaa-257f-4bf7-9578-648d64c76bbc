# дефолтний ini файл для MySQL 8.0.30, який іде з OS Panel 5.4.4

[client]

character_sets_dir				= "%sprogdir%/modules/database/%mysql_driver%/share/charsets"
plugin_dir								= "%sprogdir%/modules/database/%mysql_driver%/lib/plugin"
port											= %mysqlport%

[mysql]

character_sets_dir				= "%sprogdir%/modules/database/%mysql_driver%/share/charsets"

[mysqld]

# General Settings

authentication_policy	= mysql_native_password,,
basedir								= "%sprogdir%/modules/database/%mysql_driver%"
bind_address						= %ip%
mysqlx_bind_address		= %ip%
character_sets_dir			= "%sprogdir%/modules/database/%mysql_driver%/share/charsets"
character_set_server		= utf8mb4
collation_server				= utf8mb4_0900_ai_ci
datadir								= "%sprogdir%/userdata/%mysql_driver%"
default_storage_engine	= InnoDB
default_time_zone			= Etc/GMT-3
explicit_defaults_for_timestamp = 1
ft_min_word_len				= 3
local_infile						= 1
lower_case_table_names	= 2
mysqlx									= 1
performance_schema			= 0
pid_file								= "%sprogdir%/userdata/temp/mysql.pid"
plugin_dir							= "%sprogdir%/modules/database/%mysql_driver%/lib/plugin"
port										= %mysqlport%
mysqlx_port						= %mysqlport%0
secure_file_priv				= "%sprogdir%/userdata/temp/upload"
server_id							= 1
#skip-grant-tables
#skip_name_resolve			= 1
tmpdir									= "%sprogdir%/userdata/temp"

# SSL Settings

mysqlx_ssl_ca					= "%sprogdir%/userdata/%mysql_driver%/ca.pem"
mysqlx_ssl_cert				= "%sprogdir%/userdata/%mysql_driver%/server-cert.pem"
mysqlx_ssl_key					= "%sprogdir%/userdata/%mysql_driver%/server-key.pem"
ssl_ca									= "%sprogdir%/userdata/%mysql_driver%/ca.pem"
ssl_cert								= "%sprogdir%/userdata/%mysql_driver%/server-cert.pem"
ssl_key								= "%sprogdir%/userdata/%mysql_driver%/server-key.pem"
tls_version						= TLSv1.2,TLSv1.3

# Connection Settings

max_connections				= 128
max_connect_errors			= 32
thread_cache_size			= 4

# InnoDB Settings

innodb_buffer_pool_size = 160M
innodb_data_file_path		= ibdata1:10M:autoextend
innodb_data_home_dir		= "%sprogdir%/userdata/%mysql_driver%"
innodb_file_per_table		= 1
#innodb_force_recovery	= 1
innodb_log_file_size		= 32M

# Logging

%log%general_log				= 1
%log%general_log_file		= "%sprogdir%/userdata/logs/%mysql_driver%_queries.log"
log_error								= "%sprogdir%/userdata/logs/%mysql_driver%_error.log"
#log_queries_not_using_indexes	= 1
#long_query_time				= 1
#slow_query_log					= 1
#slow_query_log_file		= "%sprogdir%/userdata/logs/%mysql_driver%_slow.log"
skip-log-bin

# MyISAM Settings

key_buffer_size					= 32M
myisam_recover_options	= backup,force

# Table Settings

table_definition_cache	= 4096
table_open_cache				= 4096
max_heap_table_size			= 128M
tmp_table_size					= 16M

[mysqldump]

quick
quote_names
max_allowed_packet			= 32M

[myisamchk]

key_buffer_size					= 32M